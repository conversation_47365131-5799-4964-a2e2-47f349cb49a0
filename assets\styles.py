"""
Modern Styling System for AbuSaker Tools
PUBG Mobile Emulator Performance Optimizer
Developed by <PERSON><PERSON> Damra
"""

import tkinter as tk
from tkinter import ttk

class ModernTheme:
    """Modern dark theme with PUBG-inspired colors"""
    
    # PUBG-inspired color palette
    COLORS = {
        # Primary colors
        'primary': '#FF6B35',           # PUBG Orange
        'primary_dark': '#E55A2B',      # Darker orange
        'secondary': '#F7931E',         # Golden orange
        'accent': '#FFD23F',            # Bright yellow
        
        # Background colors
        'bg_primary': '#1A1A1A',        # Dark background
        'bg_secondary': '#2D2D2D',      # Lighter dark
        'bg_tertiary': '#3D3D3D',       # Card background
        'bg_hover': '#4D4D4D',          # Hover state
        
        # Text colors
        'text_primary': '#FFFFFF',      # White text
        'text_secondary': '#CCCCCC',    # Light gray
        'text_muted': '#999999',        # Muted gray
        'text_accent': '#FF6B35',       # Accent text
        
        # Status colors
        'success': '#4CAF50',           # Green
        'warning': '#FF9800',           # Orange
        'error': '#F44336',             # Red
        'info': '#2196F3',              # Blue
        
        # Border colors
        'border': '#555555',            # Border gray
        'border_light': '#777777',      # Lighter border
        'border_accent': '#FF6B35',     # Accent border
    }
    
    # Typography
    FONTS = {
        'title': ('Segoe UI', 18, 'bold'),
        'heading': ('Segoe UI', 14, 'bold'),
        'subheading': ('Segoe UI', 12, 'bold'),
        'body': ('Segoe UI', 10),
        'small': ('Segoe UI', 9),
        'mono': ('Consolas', 9),
        'button': ('Segoe UI', 10, 'bold'),
    }
    
    # Spacing
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 12,
        'lg': 16,
        'xl': 24,
        'xxl': 32,
    }
    
    def __init__(self, root):
        self.root = root
        self.style = ttk.Style()
        self._setup_theme()
    
    def _setup_theme(self):
        """Setup the modern theme"""
        # Configure root window
        self.root.configure(bg=self.COLORS['bg_primary'])
        
        # Use a modern theme as base
        try:
            self.style.theme_use('clam')
        except:
            self.style.theme_use('default')
        
        # Configure styles
        self._configure_labels()
        self._configure_buttons()
        self._configure_frames()
        self._configure_progressbars()
        self._configure_combobox()
        self._configure_checkbutton()
        self._configure_separator()
    
    def _configure_labels(self):
        """Configure label styles"""
        # Title label
        self.style.configure('Title.TLabel',
            font=self.FONTS['title'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['bg_primary']
        )
        
        # Heading label
        self.style.configure('Heading.TLabel',
            font=self.FONTS['heading'],
            foreground=self.COLORS['primary'],
            background=self.COLORS['bg_primary']
        )
        
        # Subheading label
        self.style.configure('Subheading.TLabel',
            font=self.FONTS['subheading'],
            foreground=self.COLORS['text_secondary'],
            background=self.COLORS['bg_primary']
        )
        
        # Body label
        self.style.configure('Body.TLabel',
            font=self.FONTS['body'],
            foreground=self.COLORS['text_secondary'],
            background=self.COLORS['bg_primary']
        )
        
        # Status labels
        self.style.configure('Status.TLabel',
            font=self.FONTS['body'],
            foreground=self.COLORS['accent'],
            background=self.COLORS['bg_primary']
        )
        
        # Developer label
        self.style.configure('Developer.TLabel',
            font=self.FONTS['small'],
            foreground=self.COLORS['text_muted'],
            background=self.COLORS['bg_primary']
        )
    
    def _configure_buttons(self):
        """Configure button styles"""
        # Primary button
        self.style.configure('Primary.TButton',
            font=self.FONTS['button'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['primary'],
            borderwidth=0,
            focuscolor='none',
            padding=(self.SPACING['md'], self.SPACING['sm'])
        )
        
        self.style.map('Primary.TButton',
            background=[('active', self.COLORS['primary_dark']),
                       ('pressed', self.COLORS['primary_dark'])]
        )
        
        # Secondary button
        self.style.configure('Secondary.TButton',
            font=self.FONTS['button'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['bg_tertiary'],
            borderwidth=1,
            focuscolor='none',
            padding=(self.SPACING['md'], self.SPACING['sm'])
        )
        
        self.style.map('Secondary.TButton',
            background=[('active', self.COLORS['bg_hover']),
                       ('pressed', self.COLORS['bg_hover'])],
            bordercolor=[('active', self.COLORS['border_accent'])]
        )
        
        # Accent button (for one-click optimization)
        self.style.configure('Accent.TButton',
            font=self.FONTS['heading'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['secondary'],
            borderwidth=0,
            focuscolor='none',
            padding=(self.SPACING['lg'], self.SPACING['md'])
        )
        
        self.style.map('Accent.TButton',
            background=[('active', self.COLORS['accent']),
                       ('pressed', self.COLORS['accent'])]
        )
    
    def _configure_frames(self):
        """Configure frame styles"""
        # Main frame
        self.style.configure('Main.TFrame',
            background=self.COLORS['bg_primary'],
            borderwidth=0
        )
        
        # Card frame
        self.style.configure('Card.TFrame',
            background=self.COLORS['bg_secondary'],
            borderwidth=1,
            relief='solid'
        )
        
        # Label frame
        self.style.configure('Modern.TLabelframe',
            background=self.COLORS['bg_secondary'],
            borderwidth=1,
            relief='solid',
            bordercolor=self.COLORS['border']
        )
        
        self.style.configure('Modern.TLabelframe.Label',
            font=self.FONTS['subheading'],
            foreground=self.COLORS['primary'],
            background=self.COLORS['bg_secondary']
        )
    
    def _configure_progressbars(self):
        """Configure progress bar styles"""
        self.style.configure('Modern.Horizontal.TProgressbar',
            background=self.COLORS['primary'],
            troughcolor=self.COLORS['bg_tertiary'],
            borderwidth=0,
            lightcolor=self.COLORS['primary'],
            darkcolor=self.COLORS['primary']
        )
    
    def _configure_combobox(self):
        """Configure combobox styles"""
        self.style.configure('Modern.TCombobox',
            font=self.FONTS['body'],
            foreground=self.COLORS['text_primary'],
            background=self.COLORS['bg_tertiary'],
            fieldbackground=self.COLORS['bg_tertiary'],
            borderwidth=1,
            bordercolor=self.COLORS['border']
        )
        
        self.style.map('Modern.TCombobox',
            bordercolor=[('active', self.COLORS['border_accent']),
                        ('focus', self.COLORS['border_accent'])]
        )
    
    def _configure_checkbutton(self):
        """Configure checkbutton styles"""
        self.style.configure('Modern.TCheckbutton',
            font=self.FONTS['body'],
            foreground=self.COLORS['text_secondary'],
            background=self.COLORS['bg_secondary'],
            focuscolor='none'
        )
        
        self.style.map('Modern.TCheckbutton',
            foreground=[('active', self.COLORS['text_primary'])]
        )
    
    def _configure_separator(self):
        """Configure separator styles"""
        self.style.configure('Modern.TSeparator',
            background=self.COLORS['border']
        )
    
    def get_color(self, color_name):
        """Get color value by name"""
        return self.COLORS.get(color_name, '#FFFFFF')
    
    def get_font(self, font_name):
        """Get font by name"""
        return self.FONTS.get(font_name, ('Segoe UI', 10))
    
    def get_spacing(self, spacing_name):
        """Get spacing value by name"""
        return self.SPACING.get(spacing_name, 8)

def apply_modern_theme(root):
    """Apply modern theme to the application"""
    return ModernTheme(root)
