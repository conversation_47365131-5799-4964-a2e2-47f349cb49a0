"""
Windows Performance Optimizer - Professional GUI Tool
Designed for PUBG Mobile Emulator Players
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import sys
import os

# Import our modules
from performance_scripts import PerformanceOptimizer
from system_monitor import SystemMonitor
from config import Config
from utils import is_admin, run_as_admin, format_bytes


class PerformanceOptimizerGUI:
    """Main GUI application class"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AbuSaker Tools - Windows Performance Optimizer")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Initialize components
        self.config = Config()
        self.optimizer = PerformanceOptimizer()
        self.monitor = SystemMonitor()
        
        # GUI variables
        self.monitoring_active = tk.BooleanVar(value=False)
        self.auto_refresh = tk.BooleanVar(value=True)
        self.selected_profile = tk.StringVar(value="pubg_mobile")
        
        # Status variables
        self.cpu_var = tk.StringVar(value="CPU: 0%")
        self.memory_var = tk.StringVar(value="Memory: 0%")
        self.processes_var = tk.StringVar(value="Processes: 0")
        self.emulators_var = tk.StringVar(value="Emulators: 0")
        
        # Check admin privileges
        if not is_admin():
            self.show_admin_warning()
        
        self.setup_gui()
        self.setup_monitoring()
        
    def show_admin_warning(self):
        """Show warning about admin privileges"""
        response = messagebox.askyesno(
            "Administrator Privileges Required",
            "This application requires administrator privileges for optimal performance.\n\n"
            "Would you like to restart as administrator?",
            icon="warning"
        )
        if response:
            run_as_admin()
            sys.exit()
    
    def setup_gui(self):
        """Setup the main GUI interface"""
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure colors for dark theme
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2E86AB')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), foreground='#A23B72')
        style.configure('Status.TLabel', font=('Arial', 10), foreground='#F18F01')
        
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🚀 AbuSaker Tools - Performance Optimizer", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Left panel - System monitoring
        self.setup_monitoring_panel(main_frame)
        
        # Center panel - Optimization controls
        self.setup_optimization_panel(main_frame)
        
        # Bottom panel - Log output
        self.setup_log_panel(main_frame)
        
        # Status bar
        self.setup_status_bar()
    
    def setup_monitoring_panel(self, parent):
        """Setup system monitoring panel"""
        monitor_frame = ttk.LabelFrame(parent, text="📊 System Monitor", padding="10")
        monitor_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Monitoring toggle
        monitor_toggle = ttk.Checkbutton(
            monitor_frame, 
            text="Real-time Monitoring",
            variable=self.monitoring_active,
            command=self.toggle_monitoring
        )
        monitor_toggle.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # System stats
        stats_labels = [
            ("CPU Usage:", self.cpu_var),
            ("Memory Usage:", self.memory_var),
            ("Running Processes:", self.processes_var),
            ("Active Emulators:", self.emulators_var)
        ]
        
        for i, (label_text, var) in enumerate(stats_labels):
            ttk.Label(monitor_frame, text=label_text, style='Header.TLabel').grid(
                row=i+1, column=0, sticky=tk.W, pady=2
            )
            ttk.Label(monitor_frame, textvariable=var, style='Status.TLabel').grid(
                row=i+1, column=1, sticky=tk.W, padx=(10, 0), pady=2
            )
        
        # Progress bars
        self.cpu_progress = ttk.Progressbar(monitor_frame, length=200, mode='determinate')
        self.cpu_progress.grid(row=1, column=2, padx=(10, 0), pady=2)
        
        self.memory_progress = ttk.Progressbar(monitor_frame, length=200, mode='determinate')
        self.memory_progress.grid(row=2, column=2, padx=(10, 0), pady=2)
        
        # Refresh button
        refresh_btn = ttk.Button(monitor_frame, text="🔄 Refresh", command=self.manual_refresh)
        refresh_btn.grid(row=5, column=0, columnspan=3, pady=(10, 0))
    
    def setup_optimization_panel(self, parent):
        """Setup optimization controls panel"""
        opt_frame = ttk.LabelFrame(parent, text="⚡ Performance Optimization", padding="10")
        opt_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        opt_frame.columnconfigure(0, weight=1)
        
        # Profile selection
        profile_frame = ttk.Frame(opt_frame)
        profile_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        profile_frame.columnconfigure(1, weight=1)
        
        ttk.Label(profile_frame, text="Optimization Profile:", style='Header.TLabel').grid(
            row=0, column=0, sticky=tk.W
        )
        
        profile_combo = ttk.Combobox(
            profile_frame,
            textvariable=self.selected_profile,
            values=["pubg_mobile", "gaming", "basic"],
            state="readonly",
            width=20
        )
        profile_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # Individual optimization buttons
        opt_buttons = [
            ("🧹 Clear Memory Cache", self.clear_memory),
            ("🌐 Clear DNS Cache", self.clear_dns),
            ("📁 Clear Temp Files", self.clear_temp_files),
            ("⚡ High Performance Mode", self.set_high_performance),
            ("🔧 Optimize Network", self.optimize_network),
            ("❌ Kill Unnecessary Processes", self.kill_processes),
            ("🎮 Optimize Emulator Priority", self.optimize_emulator),
            ("🚫 Disable Game Mode", self.disable_game_mode)
        ]
        
        for i, (text, command) in enumerate(opt_buttons):
            btn = ttk.Button(opt_frame, text=text, command=command, width=30)
            btn.grid(row=i+1, column=0, pady=2, sticky=(tk.W, tk.E))
        
        # Separator
        ttk.Separator(opt_frame, orient='horizontal').grid(
            row=len(opt_buttons)+1, column=0, sticky=(tk.W, tk.E), pady=10
        )
        
        # One-click optimization
        one_click_btn = ttk.Button(
            opt_frame,
            text="🚀 ONE-CLICK OPTIMIZATION",
            command=self.run_full_optimization,
            style='Accent.TButton'
        )
        one_click_btn.grid(row=len(opt_buttons)+2, column=0, pady=10, sticky=(tk.W, tk.E))
        
        # Configure accent button style
        style = ttk.Style()
        style.configure('Accent.TButton', font=('Arial', 12, 'bold'))
    
    def setup_log_panel(self, parent):
        """Setup log output panel"""
        log_frame = ttk.LabelFrame(parent, text="📝 Activity Log", padding="10")
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=8,
            wrap=tk.WORD,
            font=('Consolas', 9)
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(log_controls, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_controls, text="Save Log", command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))
        
        # Initial log message
        self.log_message("🎮 AbuSaker Tools - Performance Optimizer Started")
        self.log_message("💡 Designed for PUBG Mobile Emulator Players")
        if is_admin():
            self.log_message("✅ Running with Administrator privileges")
        else:
            self.log_message("⚠️ Running without Administrator privileges - some features may be limited")
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        self.status_text = tk.StringVar(value="Ready")
        status_label = ttk.Label(self.status_bar, textvariable=self.status_text)
        status_label.pack(side=tk.LEFT, padx=10, pady=5)

    def setup_monitoring(self):
        """Setup system monitoring"""
        self.monitor.add_callback(self.update_monitoring_display)

    def toggle_monitoring(self):
        """Toggle real-time monitoring"""
        if self.monitoring_active.get():
            self.monitor.start_monitoring()
            self.log_message("📊 Real-time monitoring started")
            self.status_text.set("Monitoring active")
        else:
            self.monitor.stop_monitoring()
            self.log_message("📊 Real-time monitoring stopped")
            self.status_text.set("Ready")

    def update_monitoring_display(self, stats):
        """Update monitoring display with new stats"""
        try:
            # Update text variables
            self.cpu_var.set(f"CPU: {stats['cpu_percent']:.1f}%")
            self.memory_var.set(f"Memory: {stats['memory_percent']:.1f}%")
            self.processes_var.set(f"Processes: {stats['running_processes']}")
            self.emulators_var.set(f"Emulators: {len(stats['emulator_processes'])}")

            # Update progress bars
            self.cpu_progress['value'] = stats['cpu_percent']
            self.memory_progress['value'] = stats['memory_percent']

            # Update status if system is under load
            if stats['cpu_percent'] > 80 or stats['memory_percent'] > 85:
                self.status_text.set("⚠️ System under high load")
            elif self.monitoring_active.get():
                self.status_text.set("Monitoring active")

        except Exception as e:
            print(f"Error updating monitoring display: {e}")

    def manual_refresh(self):
        """Manually refresh system stats"""
        stats = self.monitor.get_current_stats()
        self.update_monitoring_display(stats)
        self.log_message("🔄 System stats refreshed")

    def log_message(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Limit log size
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "10.0")

    def clear_log(self):
        """Clear the log"""
        self.log_text.delete("1.0", tk.END)
        self.log_message("📝 Log cleared")

    def save_log(self):
        """Save log to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"optimization_log_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get("1.0", tk.END))

            self.log_message(f"💾 Log saved to {filename}")
            messagebox.showinfo("Log Saved", f"Log saved to {filename}")

        except Exception as e:
            self.log_message(f"❌ Error saving log: {str(e)}")
            messagebox.showerror("Error", f"Failed to save log: {str(e)}")

    def run_optimization_thread(self, func, *args):
        """Run optimization function in a separate thread"""
        def worker():
            try:
                self.status_text.set("Running optimization...")
                success, message = func(*args)

                if success:
                    self.log_message(f"✅ {message}")
                else:
                    self.log_message(f"❌ {message}")

                self.status_text.set("Ready")

            except Exception as e:
                self.log_message(f"❌ Error: {str(e)}")
                self.status_text.set("Error occurred")

        thread = threading.Thread(target=worker, daemon=True)
        thread.start()

    # Optimization button handlers
    def clear_memory(self):
        """Clear memory cache"""
        self.run_optimization_thread(self.optimizer.clear_memory_cache)

    def clear_dns(self):
        """Clear DNS cache"""
        self.run_optimization_thread(self.optimizer.clear_dns_cache)

    def clear_temp_files(self):
        """Clear temporary files"""
        self.run_optimization_thread(self.optimizer.clear_temp_files)

    def set_high_performance(self):
        """Set high performance mode"""
        self.run_optimization_thread(self.optimizer.set_high_performance_mode)

    def optimize_network(self):
        """Optimize network settings"""
        self.run_optimization_thread(self.optimizer.optimize_network_settings)

    def kill_processes(self):
        """Kill unnecessary processes"""
        response = messagebox.askyesno(
            "Confirm Action",
            "This will close unnecessary applications like browsers, Discord, etc.\n\n"
            "Continue?",
            icon="warning"
        )
        if response:
            self.run_optimization_thread(self.optimizer.kill_unnecessary_processes)

    def optimize_emulator(self):
        """Optimize emulator priority"""
        self.run_optimization_thread(self.optimizer.optimize_emulator_priority)

    def disable_game_mode(self):
        """Disable Windows Game Mode"""
        self.run_optimization_thread(self.optimizer.disable_windows_game_mode)

    def run_full_optimization(self):
        """Run full optimization suite"""
        profile = self.selected_profile.get()

        response = messagebox.askyesno(
            "Full Optimization",
            f"This will run the complete {profile} optimization profile.\n\n"
            "This may take a few minutes and will:\n"
            "• Clear system caches and temporary files\n"
            "• Optimize network settings\n"
            "• Set high performance mode\n"
            "• Close unnecessary applications\n"
            "• Optimize emulator settings\n\n"
            "Continue?",
            icon="question"
        )

        if response:
            def full_optimization_worker():
                try:
                    self.status_text.set("Running full optimization...")
                    self.log_message(f"🚀 Starting full optimization ({profile} profile)")

                    success, results = self.optimizer.run_full_optimization(profile)

                    for result in results:
                        self.log_message(result)

                    if success:
                        self.log_message("🎉 Full optimization completed successfully!")
                        messagebox.showinfo(
                            "Optimization Complete",
                            "Full optimization completed successfully!\n\n"
                            "Your system should now perform better for gaming."
                        )
                    else:
                        self.log_message("⚠️ Optimization completed with some issues")
                        messagebox.showwarning(
                            "Optimization Complete",
                            "Optimization completed but some operations failed.\n\n"
                            "Check the log for details."
                        )

                    self.status_text.set("Ready")

                except Exception as e:
                    self.log_message(f"❌ Error during full optimization: {str(e)}")
                    self.status_text.set("Error occurred")
                    messagebox.showerror("Error", f"Optimization failed: {str(e)}")

            thread = threading.Thread(target=full_optimization_worker, daemon=True)
            thread.start()

    def on_closing(self):
        """Handle application closing"""
        if self.monitoring_active.get():
            self.monitor.stop_monitoring()
        self.root.destroy()

    def run(self):
        """Start the GUI application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = PerformanceOptimizerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Fatal Error", f"Application failed to start: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
