"""
System monitoring module for Windows Performance Optimizer
"""
import psutil
import threading
import time
from typing import Dict, List, Callable, Optional
from utils import format_bytes, get_emulator_processes


class SystemMonitor:
    """Real-time system monitoring class"""
    
    def __init__(self, update_interval: float = 1.0):
        self.update_interval = update_interval
        self.monitoring = False
        self.monitor_thread = None
        self.callbacks: List[Callable] = []
        
        # Current system stats
        self.stats = {
            'cpu_percent': 0.0,
            'memory_percent': 0.0,
            'memory_used': 0,
            'memory_total': 0,
            'memory_available': 0,
            'disk_usage': {},
            'network_io': {'bytes_sent': 0, 'bytes_recv': 0},
            'running_processes': 0,
            'emulator_processes': [],
            'top_processes': []
        }
    
    def add_callback(self, callback: Callable):
        """Add callback function to be called when stats are updated"""
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable):
        """Remove callback function"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def start_monitoring(self):
        """Start system monitoring in a separate thread"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                self._update_stats()
                # Call all registered callbacks
                for callback in self.callbacks:
                    try:
                        callback(self.stats)
                    except Exception as e:
                        print(f"Error in monitor callback: {e}")
                
                time.sleep(self.update_interval)
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                time.sleep(self.update_interval)
    
    def _update_stats(self):
        """Update system statistics"""
        # CPU usage
        self.stats['cpu_percent'] = psutil.cpu_percent(interval=None)
        
        # Memory usage
        memory = psutil.virtual_memory()
        self.stats['memory_percent'] = memory.percent
        self.stats['memory_used'] = memory.used
        self.stats['memory_total'] = memory.total
        self.stats['memory_available'] = memory.available
        
        # Disk usage for C: drive
        try:
            disk = psutil.disk_usage('C:')
            self.stats['disk_usage'] = {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': (disk.used / disk.total) * 100
            }
        except:
            self.stats['disk_usage'] = {}
        
        # Network I/O
        try:
            net_io = psutil.net_io_counters()
            self.stats['network_io'] = {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv
            }
        except:
            self.stats['network_io'] = {'bytes_sent': 0, 'bytes_recv': 0}
        
        # Process information
        self._update_process_info()
    
    def _update_process_info(self):
        """Update process-related information"""
        try:
            processes = list(psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']))
            self.stats['running_processes'] = len(processes)
            
            # Find emulator processes
            emulator_names = get_emulator_processes()
            emulator_processes = []
            
            for proc in processes:
                try:
                    if proc.info['name'] in emulator_names:
                        emulator_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cpu_percent': proc.info['cpu_percent'] or 0,
                            'memory_percent': proc.info['memory_percent'] or 0
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            self.stats['emulator_processes'] = emulator_processes
            
            # Get top CPU consuming processes
            try:
                top_processes = sorted(
                    [p for p in processes if p.info['cpu_percent'] and p.info['cpu_percent'] > 0],
                    key=lambda x: x.info['cpu_percent'],
                    reverse=True
                )[:5]
                
                self.stats['top_processes'] = [
                    {
                        'name': proc.info['name'],
                        'cpu_percent': proc.info['cpu_percent'],
                        'memory_percent': proc.info['memory_percent'] or 0
                    }
                    for proc in top_processes
                ]
            except:
                self.stats['top_processes'] = []
                
        except Exception as e:
            print(f"Error updating process info: {e}")
    
    def get_current_stats(self) -> Dict:
        """Get current system statistics"""
        if not self.monitoring:
            self._update_stats()
        return self.stats.copy()
    
    def get_formatted_stats(self) -> Dict[str, str]:
        """Get formatted system statistics for display"""
        stats = self.get_current_stats()
        
        formatted = {
            'cpu': f"{stats['cpu_percent']:.1f}%",
            'memory': f"{stats['memory_percent']:.1f}%",
            'memory_used': format_bytes(stats['memory_used']),
            'memory_total': format_bytes(stats['memory_total']),
            'memory_available': format_bytes(stats['memory_available']),
            'processes': str(stats['running_processes']),
            'emulators': str(len(stats['emulator_processes']))
        }
        
        if stats['disk_usage']:
            formatted['disk'] = f"{stats['disk_usage']['percent']:.1f}%"
            formatted['disk_free'] = format_bytes(stats['disk_usage']['free'])
        
        return formatted
    
    def is_system_under_load(self, cpu_threshold: float = 80, memory_threshold: float = 85) -> bool:
        """Check if system is under heavy load"""
        stats = self.get_current_stats()
        return (stats['cpu_percent'] > cpu_threshold or 
                stats['memory_percent'] > memory_threshold)
    
    def get_emulator_status(self) -> Dict:
        """Get status of running emulators"""
        stats = self.get_current_stats()
        emulators = stats['emulator_processes']
        
        if not emulators:
            return {'status': 'none', 'count': 0, 'details': []}
        
        total_cpu = sum(emu['cpu_percent'] for emu in emulators)
        total_memory = sum(emu['memory_percent'] for emu in emulators)
        
        return {
            'status': 'running',
            'count': len(emulators),
            'total_cpu': total_cpu,
            'total_memory': total_memory,
            'details': emulators
        }
