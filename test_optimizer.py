"""
Test script for AbuSaker Tools - Windows Performance Optimizer
This script tests all major components without making system changes
"""
import sys
import os
import traceback
from datetime import datetime


def test_imports():
    """Test if all modules can be imported"""
    print("🧪 Testing module imports...")
    
    modules_to_test = [
        ('utils', 'Utility functions'),
        ('config', 'Configuration management'),
        ('system_monitor', 'System monitoring'),
        ('performance_scripts', 'Performance optimization'),
        ('main', 'Main GUI application')
    ]
    
    results = []
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}: {description}")
            results.append(True)
        except Exception as e:
            print(f"❌ {module_name}: {description} - Error: {e}")
            results.append(False)
    
    return all(results)


def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n📦 Testing dependencies...")
    
    dependencies = [
        ('tkinter', 'GUI framework'),
        ('psutil', 'System monitoring'),
        ('threading', 'Multi-threading support'),
        ('json', 'Configuration files'),
        ('ctypes', 'Windows API access'),
        ('winreg', 'Windows registry access')
    ]
    
    results = []
    for dep_name, description in dependencies:
        try:
            __import__(dep_name)
            print(f"✅ {dep_name}: {description}")
            results.append(True)
        except Exception as e:
            print(f"❌ {dep_name}: {description} - Error: {e}")
            results.append(False)
    
    return all(results)


def test_system_monitor():
    """Test system monitoring functionality"""
    print("\n📊 Testing system monitoring...")
    
    try:
        from system_monitor import SystemMonitor
        
        monitor = SystemMonitor()
        print("✅ SystemMonitor created successfully")
        
        # Test getting current stats
        stats = monitor.get_current_stats()
        print(f"✅ Current CPU: {stats['cpu_percent']:.1f}%")
        print(f"✅ Current Memory: {stats['memory_percent']:.1f}%")
        print(f"✅ Running Processes: {stats['running_processes']}")
        
        # Test formatted stats
        formatted = monitor.get_formatted_stats()
        print(f"✅ Formatted stats: {len(formatted)} items")
        
        # Test emulator detection
        emulator_status = monitor.get_emulator_status()
        print(f"✅ Emulator status: {emulator_status['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ System monitoring test failed: {e}")
        traceback.print_exc()
        return False


def test_configuration():
    """Test configuration management"""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config import Config
        
        config = Config("test_config.json")
        print("✅ Config created successfully")
        
        # Test getting values
        theme = config.get("theme", "dark")
        print(f"✅ Theme setting: {theme}")
        
        # Test setting values
        config.set("test.value", "test_data")
        retrieved = config.get("test.value")
        print(f"✅ Set/Get test: {retrieved}")
        
        # Test profiles
        profiles = config.get("optimization_profiles", {})
        print(f"✅ Optimization profiles: {len(profiles)} available")
        
        # Clean up test config
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False


def test_utilities():
    """Test utility functions"""
    print("\n🔧 Testing utilities...")
    
    try:
        from utils import is_admin, format_bytes, get_emulator_processes
        
        # Test admin check
        admin_status = is_admin()
        print(f"✅ Admin check: {'Yes' if admin_status else 'No'}")
        
        # Test byte formatting
        formatted = format_bytes(1024 * 1024 * 1024)
        print(f"✅ Byte formatting: {formatted}")
        
        # Test emulator process list
        emulators = get_emulator_processes()
        print(f"✅ Emulator processes: {len(emulators)} supported")
        
        return True
        
    except Exception as e:
        print(f"❌ Utilities test failed: {e}")
        traceback.print_exc()
        return False


def test_performance_optimizer():
    """Test performance optimizer (safe tests only)"""
    print("\n⚡ Testing performance optimizer...")
    
    try:
        from performance_scripts import PerformanceOptimizer
        
        optimizer = PerformanceOptimizer()
        print("✅ PerformanceOptimizer created successfully")
        
        # Test safe methods that don't modify system
        print("✅ Optimizer methods available:")
        methods = [method for method in dir(optimizer) if not method.startswith('_')]
        for method in methods[:5]:  # Show first 5 methods
            print(f"   - {method}")
        
        print(f"✅ Total optimization methods: {len(methods)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance optimizer test failed: {e}")
        traceback.print_exc()
        return False


def test_gui_components():
    """Test GUI components without showing the window"""
    print("\n🖥️ Testing GUI components...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Create a test root window (hidden)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Test basic tkinter components
        frame = ttk.Frame(root)
        label = ttk.Label(frame, text="Test")
        button = ttk.Button(frame, text="Test Button")
        progress = ttk.Progressbar(frame)
        
        print("✅ Basic tkinter components work")
        
        # Test if we can import the main GUI class
        from main import PerformanceOptimizerGUI
        print("✅ Main GUI class can be imported")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI components test failed: {e}")
        traceback.print_exc()
        return False


def run_all_tests():
    """Run all tests and provide summary"""
    print("🧪 AbuSaker Tools - Performance Optimizer Test Suite")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Module Imports", test_imports),
        ("System Monitor", test_system_monitor),
        ("Configuration", test_configuration),
        ("Utilities", test_utilities),
        ("Performance Optimizer", test_performance_optimizer),
        ("GUI Components", test_gui_components)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
    elif passed >= total * 0.8:
        print("⚠️ Most tests passed. Application should work with minor issues.")
    else:
        print("❌ Multiple test failures. Please check dependencies and setup.")
    
    print("=" * 60)
    return passed == total


if __name__ == "__main__":
    try:
        success = run_all_tests()
        input("\nPress Enter to exit...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Tests interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        traceback.print_exc()
        input("Press Enter to exit...")
        sys.exit(1)
