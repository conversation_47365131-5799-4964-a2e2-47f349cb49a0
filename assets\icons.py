"""
SVG Icons and Assets for AbuSaker Tools
PUBG Mobile Emulator Performance Optimizer
Developed by Hamza Damra
"""

import tkinter as tk
from tkinter import PhotoImage
import base64
from io import BytesIO

class IconManager:
    """Manages SVG icons and converts them to PhotoImage objects for tkinter"""
    
    def __init__(self):
        self.icons = {}
        self._load_icons()
    
    def _load_icons(self):
        """Load all SVG icons as base64 encoded strings"""
        
        # PUBG Mobile Logo Icon
        self.icons['pubg_logo'] = '''
        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="pubgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#F7931E;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#FFD23F;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="32" height="32" rx="6" fill="url(#pubgGradient)"/>
            <text x="16" y="20" font-family="Arial Black" font-size="12" font-weight="bold" 
                  text-anchor="middle" fill="white">P</text>
            <circle cx="8" cy="8" r="2" fill="white" opacity="0.8"/>
            <circle cx="24" cy="8" r="2" fill="white" opacity="0.8"/>
            <rect x="6" y="24" width="20" height="2" rx="1" fill="white" opacity="0.9"/>
        </svg>
        '''
        
        # Performance Icon
        self.icons['performance'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="perfGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:1" />
                </linearGradient>
            </defs>
            <circle cx="12" cy="12" r="10" fill="url(#perfGradient)" stroke="#2E7D32" stroke-width="2"/>
            <path d="M8 12l3 3 6-6" stroke="white" stroke-width="2" fill="none" stroke-linecap="round"/>
        </svg>
        '''
        
        # System Monitor Icon
        self.icons['monitor'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="monitorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#03DAC6;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect x="2" y="4" width="20" height="12" rx="2" fill="url(#monitorGradient)" stroke="#1976D2" stroke-width="1"/>
            <rect x="4" y="6" width="16" height="8" fill="#0D47A1" opacity="0.3"/>
            <path d="M6 8h4v2h-4zm6 0h6v1h-6zm0 2h4v1h-4z" fill="#00E676"/>
            <rect x="9" y="18" width="6" height="2" rx="1" fill="#666"/>
        </svg>
        '''
        
        # Memory Icon
        self.icons['memory'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="memGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#E91E63;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect x="4" y="6" width="16" height="12" rx="2" fill="url(#memGradient)" stroke="#7B1FA2" stroke-width="1"/>
            <rect x="6" y="8" width="3" height="8" fill="white" opacity="0.8"/>
            <rect x="10" y="8" width="3" height="8" fill="white" opacity="0.6"/>
            <rect x="14" y="8" width="3" height="8" fill="white" opacity="0.4"/>
        </svg>
        '''
        
        # Network Icon
        self.icons['network'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="netGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#FFC107;stop-opacity:1" />
                </linearGradient>
            </defs>
            <circle cx="12" cy="12" r="2" fill="url(#netGradient)"/>
            <path d="M8.5 8.5c1.9-1.9 5.1-1.9 7 0" stroke="url(#netGradient)" stroke-width="2" fill="none"/>
            <path d="M5.5 5.5c3.8-3.8 9.2-3.8 13 0" stroke="url(#netGradient)" stroke-width="2" fill="none"/>
            <path d="M15.5 15.5c-1.9 1.9-5.1 1.9-7 0" stroke="url(#netGradient)" stroke-width="2" fill="none"/>
            <path d="M18.5 18.5c-3.8 3.8-9.2 3.8-13 0" stroke="url(#netGradient)" stroke-width="2" fill="none"/>
        </svg>
        '''
        
        # Clean Icon
        self.icons['clean'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="cleanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#00BCD4;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#4CAF50;stop-opacity:1" />
                </linearGradient>
            </defs>
            <circle cx="12" cy="12" r="10" fill="url(#cleanGradient)" stroke="#00838F" stroke-width="1"/>
            <path d="M8 12l2 2 6-6" stroke="white" stroke-width="2" fill="none" stroke-linecap="round"/>
            <circle cx="12" cy="12" r="6" fill="none" stroke="white" stroke-width="1" opacity="0.3"/>
        </svg>
        '''
        
        # Gaming Icon
        self.icons['gaming'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="gameGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#673AB7;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#9C27B0;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect x="3" y="8" width="18" height="8" rx="4" fill="url(#gameGradient)" stroke="#512DA8" stroke-width="1"/>
            <circle cx="8" cy="12" r="1.5" fill="white"/>
            <circle cx="16" cy="10" r="1" fill="white"/>
            <circle cx="18" cy="12" r="1" fill="white"/>
            <circle cx="16" cy="14" r="1" fill="white"/>
            <circle cx="14" cy="12" r="1" fill="white"/>
        </svg>
        '''
        
        # Settings Icon
        self.icons['settings'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="settingsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#607D8B;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#90A4AE;stop-opacity:1" />
                </linearGradient>
            </defs>
            <circle cx="12" cy="12" r="3" fill="url(#settingsGradient)"/>
            <path d="M12 1v6m0 10v6m11-7h-6m-10 0H1m15.5-6.5l-4.2 4.2m-8.6 0L7.9 7.9m0 8.2l4.2-4.2m8.6 0l-4.2 4.2" 
                  stroke="url(#settingsGradient)" stroke-width="2" fill="none"/>
        </svg>
        '''
        
        # Refresh Icon
        self.icons['refresh'] = '''
        <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="refreshGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:1" />
                </linearGradient>
            </defs>
            <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
                  stroke="url(#refreshGradient)" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        '''
    
    def get_icon_data(self, icon_name):
        """Get SVG data for an icon"""
        return self.icons.get(icon_name, '')
    
    def create_png_from_svg(self, svg_data, size=(24, 24)):
        """Convert SVG to PNG data (placeholder - would need additional libraries)"""
        # This is a placeholder - in a real implementation, you'd use libraries like cairosvg
        # For now, we'll return None and fall back to text labels
        return None

# Global icon manager instance
icon_manager = IconManager()
