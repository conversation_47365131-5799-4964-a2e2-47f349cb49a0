# 🚀 AbuSaker Tools - Windows Performance Optimizer

A professional Python GUI application designed to enhance Windows performance specifically for PUBG Mobile emulator players. This tool provides comprehensive system optimization features with an intuitive interface.

## ✨ Features

### 🎮 Gaming-Focused Optimizations
- **PUBG Mobile Profile**: Optimized specifically for mobile emulator gaming
- **Emulator Priority Optimization**: Automatically sets high priority for emulator processes
- **Game Mode Disable**: Disables Windows Game Mode that can interfere with emulators
- **Network Optimization**: Optimizes TCP/IP settings for reduced latency

### 🧹 System Cleanup
- **Memory Cache Clearing**: Clears system memory cache and reduces RAM usage
- **DNS Cache Flush**: Clears DNS cache for improved network performance
- **Temporary Files Cleanup**: Removes temporary files and browser caches
- **Recycle Bin Emptying**: Clears recycle bin to free disk space

### ⚡ Performance Enhancements
- **High Performance Mode**: Sets Windows to high performance power plan
- **Process Management**: Kills unnecessary background processes
- **Virtual Memory Optimization**: Optimizes page file settings
- **Network Stack Reset**: Resets network adapters and TCP/IP stack

### 📊 Real-Time Monitoring
- **CPU Usage Monitoring**: Real-time CPU usage tracking
- **Memory Usage Display**: Live memory consumption monitoring
- **Process Counting**: Shows running processes and active emulators
- **System Load Alerts**: Warns when system is under high load

### 🎯 Optimization Profiles
- **PUBG Mobile**: Complete optimization for mobile gaming
- **Gaming**: General gaming performance optimization
- **Basic**: Simple cleanup and maintenance

## 🛠️ Installation

### Prerequisites
- Windows 10/11
- Python 3.7 or higher
- Administrator privileges (recommended)

### Setup Instructions

1. **Clone or Download** the project files to your desired directory

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the Application**:
   ```bash
   python main.py
   ```

### Required Python Packages
- `psutil` - System and process monitoring
- `customtkinter` - Modern GUI components
- `pillow` - Image processing support

## 🚀 Usage

### Getting Started
1. **Launch the application** by running `main.py`
2. **Grant administrator privileges** when prompted (recommended for full functionality)
3. **Select your optimization profile** from the dropdown menu
4. **Enable real-time monitoring** to track system performance

### Individual Optimizations
Click any of the individual optimization buttons to run specific optimizations:

- **🧹 Clear Memory Cache**: Frees up RAM by clearing system caches
- **🌐 Clear DNS Cache**: Flushes DNS cache for better network performance
- **📁 Clear Temp Files**: Removes temporary files and browser caches
- **⚡ High Performance Mode**: Sets Windows to maximum performance
- **🔧 Optimize Network**: Optimizes network settings for gaming
- **❌ Kill Unnecessary Processes**: Closes resource-heavy background apps
- **🎮 Optimize Emulator Priority**: Sets high priority for emulator processes
- **🚫 Disable Game Mode**: Disables Windows Game Mode

### One-Click Optimization
Use the **🚀 ONE-CLICK OPTIMIZATION** button to run a complete optimization suite based on your selected profile.

### Monitoring Features
- Toggle **Real-time Monitoring** to see live system stats
- View **CPU and Memory usage** with progress bars
- Monitor **running processes** and **active emulators**
- Check the **Activity Log** for detailed operation results

## 📁 File Structure

```
AbuSaker Tools/
├── main.py                 # Main GUI application
├── performance_scripts.py  # Optimization functions
├── system_monitor.py      # Real-time system monitoring
├── config.py              # Configuration management
├── utils.py               # Utility functions
├── requirements.txt       # Python dependencies
├── README.md             # This documentation
└── logs/                 # Generated log files
    └── optimization.log  # Optimization history
```

## ⚙️ Configuration

The application creates a `config.json` file to store settings:

- **Optimization Profiles**: Customize which optimizations run for each profile
- **Monitoring Settings**: Configure monitoring intervals and thresholds
- **UI Preferences**: Theme and display options
- **Advanced Options**: Registry backup and logging settings

## 🎮 PUBG Mobile Emulator Support

This tool is specifically optimized for popular Android emulators:

- **BlueStacks** (HD-Player.exe)
- **NoxPlayer** (Nox.exe)
- **MEmu** (MEmu.exe)
- **LDPlayer** (LdPlayer.exe)
- **SmartGaGa** (SmartGaGa.exe)
- **GameLoop** (GameLoop.exe)
- **MSI App Player** (MSIAppPlayer.exe)

## 🔒 Security & Safety

- **Administrator Privileges**: Required for system-level optimizations
- **Registry Backup**: Automatically backs up registry changes (configurable)
- **Safe Process Termination**: Only terminates non-essential processes
- **Logging**: All operations are logged for transparency
- **Restore Points**: Option to create system restore points

## 📝 Logging

All optimization activities are logged to:
- **GUI Log Panel**: Real-time activity display
- **Log Files**: Saved to `logs/optimization.log`
- **Exportable Logs**: Save logs with timestamps

## ⚠️ Important Notes

1. **Administrator Rights**: Run as administrator for full functionality
2. **Emulator Compatibility**: Ensure your emulator is supported
3. **System Backup**: Consider creating a system restore point before major optimizations
4. **Antivirus**: Some antivirus software may flag system optimization tools
5. **Performance**: Results may vary based on system specifications

## 🐛 Troubleshooting

### Common Issues

**Application won't start:**
- Ensure Python 3.7+ is installed
- Install required dependencies: `pip install -r requirements.txt`
- Run as administrator

**Optimizations not working:**
- Verify administrator privileges
- Check Windows version compatibility
- Review activity log for error details

**Emulator not detected:**
- Ensure emulator is running
- Check if emulator process name is in supported list
- Restart monitoring

## 📞 Support

For issues, suggestions, or contributions:
- Check the activity log for error details
- Ensure all dependencies are installed
- Verify administrator privileges
- Review the troubleshooting section

## 📄 License

This project is created for educational and personal use. Use at your own discretion and always backup your system before making significant changes.

---

**🎮 Optimized for PUBG Mobile Emulator Players**  
**⚡ Enhanced Gaming Performance**  
**🛡️ Safe and Reliable**
