@echo off
title AbuSaker Tools - Windows Performance Optimizer
echo.
echo ========================================
echo  AbuSaker Tools - Performance Optimizer
echo ========================================
echo.
echo Starting the application...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

REM Install dependencies if requirements.txt exists
if exist requirements.txt (
    echo Installing/updating dependencies...
    pip install -r requirements.txt
    echo.
)

REM Run the application
echo Launching Performance Optimizer...
python main.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Application exited with an error.
    pause
)
