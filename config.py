"""
Configuration management for Windows Performance Optimizer
"""
import json
import os
from typing import Dict, Any


class Config:
    """Configuration manager for the application"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.default_config = {
            "auto_admin_prompt": True,
            "show_confirmations": True,
            "auto_refresh_interval": 2000,  # milliseconds
            "theme": "dark",
            "optimization_profiles": {
                "gaming": {
                    "name": "Gaming Performance",
                    "enabled_optimizations": [
                        "clear_memory",
                        "clear_dns",
                        "clear_temp_files",
                        "set_high_performance",
                        "optimize_network",
                        "kill_unnecessary_processes"
                    ]
                },
                "pubg_mobile": {
                    "name": "PUBG Mobile Optimized",
                    "enabled_optimizations": [
                        "clear_memory",
                        "clear_dns",
                        "clear_temp_files",
                        "set_high_performance",
                        "optimize_network",
                        "kill_unnecessary_processes",
                        "optimize_emulator_priority",
                        "disable_game_mode",
                        "clear_standby_memory"
                    ]
                },
                "basic": {
                    "name": "Basic Cleanup",
                    "enabled_optimizations": [
                        "clear_temp_files",
                        "clear_dns",
                        "empty_recycle_bin"
                    ]
                }
            },
            "monitoring": {
                "show_cpu": True,
                "show_memory": True,
                "show_network": True,
                "show_temperature": False,
                "alert_cpu_threshold": 90,
                "alert_memory_threshold": 85
            },
            "advanced": {
                "backup_registry": True,
                "create_restore_point": False,
                "verbose_logging": True
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                # Merge with defaults to ensure all keys exist
                return self._merge_configs(self.default_config, loaded_config)
            except Exception as e:
                print(f"Error loading config: {e}")
                return self.default_config.copy()
        else:
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """Save configuration to file"""
        try:
            config_to_save = config if config is not None else self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'monitoring.show_cpu')"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config_ref = self.config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config_ref:
                config_ref[key] = {}
            config_ref = config_ref[key]
        
        # Set the value
        config_ref[keys[-1]] = value
        self.save_config()
    
    def _merge_configs(self, default: Dict, loaded: Dict) -> Dict:
        """Recursively merge loaded config with default config"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get_optimization_profile(self, profile_name: str) -> Dict[str, Any]:
        """Get optimization profile by name"""
        return self.config.get("optimization_profiles", {}).get(profile_name, {})
    
    def add_optimization_profile(self, name: str, profile: Dict[str, Any]) -> bool:
        """Add or update optimization profile"""
        try:
            if "optimization_profiles" not in self.config:
                self.config["optimization_profiles"] = {}
            
            self.config["optimization_profiles"][name] = profile
            return self.save_config()
        except Exception:
            return False
    
    def delete_optimization_profile(self, name: str) -> bool:
        """Delete optimization profile"""
        try:
            if name in self.config.get("optimization_profiles", {}):
                del self.config["optimization_profiles"][name]
                return self.save_config()
            return False
        except Exception:
            return False
