"""
Simple launcher for AbuSaker Tools - Windows Performance Optimizer
This launcher handles dependency installation and admin privileges
"""
import sys
import subprocess
import os
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required!")
        print(f"Current version: {sys.version}")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def install_dependencies():
    """Install required dependencies"""
    print("📦 Checking dependencies...")
    
    required_packages = ['psutil', 'customtkinter', 'pillow']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📥 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', '--user'
            ] + missing_packages)
            print("✅ Dependencies installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies!")
            print("Please run: pip install psutil customtkinter pillow")
            input("Press Enter to exit...")
            sys.exit(1)
    else:
        print("✅ All dependencies are installed")


def check_admin_privileges():
    """Check if running with admin privileges"""
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            print("✅ Running with Administrator privileges")
        else:
            print("⚠️ Not running as Administrator")
            print("Some features may be limited without admin privileges")
        return is_admin
    except:
        print("⚠️ Could not check admin privileges")
        return False


def launch_application():
    """Launch the main application"""
    print("\n🚀 Launching AbuSaker Tools - Performance Optimizer...")
    
    try:
        # Import and run the main application
        from main import main
        main()
    except ImportError as e:
        print(f"❌ Failed to import main application: {e}")
        print("Make sure all files are in the same directory")
        input("Press Enter to exit...")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Application error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)


def main():
    """Main launcher function"""
    print("=" * 60)
    print("🎮 AbuSaker Tools - Windows Performance Optimizer")
    print("=" * 60)
    print("🎯 Designed for PUBG Mobile Emulator Players")
    print("⚡ Professional Windows Performance Enhancement Tool")
    print("=" * 60)
    print()
    
    # Check Python version
    check_python_version()
    print()
    
    # Install dependencies
    install_dependencies()
    print()
    
    # Check admin privileges
    check_admin_privileges()
    print()
    
    # Launch application
    launch_application()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Launcher interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
